{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 2141450488703505184], [17045726903344650895, "build_script_build", false, 9974744449604558309], [8324462083842905811, "build_script_build", false, 12819717098292979422]], "local": [{"RerunIfChanged": {"output": "release\\build\\app-fb7a5010e2effbb1\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}