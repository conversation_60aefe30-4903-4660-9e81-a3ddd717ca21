"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import {
  Eye,
  EyeOff,
  User,
  Lock,
  X,
  CheckCircle,
  ChevronRight,
  History,
  Upload,
  FileText,
  AlertCircle,
  Loader2,
  Clock,
} from "lucide-react"

import { TauriApiClient, UserCredentials } from "@/lib/tauri-api"
import * as XLSX from 'xlsx'

interface SubmissionResult {
  id: string
  storeName: string
  description: string
  status: "success" | "error"
  timestamp: Date
  error?: string
}

interface BatchTask {
  id: string
  taskId: string
  date: string
  progress: number
  completed: number
  total: number
  status: "pending" | "processing" | "completed" | "failed"
  currentBatch?: number
  totalBatches?: number
  batchSize?: number
  startTime?: Date
  estimatedEndTime?: Date
  details?: SubmissionResult[]
}

// Excel工单数据接口
interface ExcelWorkOrder {
  storeName: string
  description: string
  result?: string
  completion_time?: string
  report_time?: string
  is_remote?: number
  has_sop?: number
  sop_description?: string
}

// Excel文件解析函数
const parseExcelFile = async (file: File): Promise<ExcelWorkOrder[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = e.target?.result
        if (!data) {
          reject(new Error('文件读取失败'))
          return
        }

        // 使用xlsx库解析Excel文件
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]

        // 将工作表转换为JSON数组
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        console.log('Excel原始数据:', jsonData)

        // 解析Excel数据，跳过标题行
        const excelData: ExcelWorkOrder[] = []

        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i] as any[]

          // 跳过空行
          if (!row || row.length === 0 || !row[0]) continue

          // 根据您的Excel格式解析数据
          // 假设列顺序：门店名称、故障描述、处理结果、是否远程、是否有SOP、SOP描述、报单时间、完成时间
          const workOrder: ExcelWorkOrder = {
            storeName: String(row[0] || '').trim(),
            description: String(row[1] || '').trim(),
            result: String(row[2] || '已完成修改').trim(),
            is_remote: row[3] ? Number(row[3]) : 0,
            has_sop: row[5] ? Number(row[5]) : 1,
            sop_description: String(row[4] || '按标准流程处理').trim(),
            report_time: row[6] ? new Date(row[6]).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' '),
            completion_time: row[7] ? new Date(row[7]).toISOString().slice(0, 19).replace('T', ' ') : new Date().toISOString().slice(0, 19).replace('T', ' ')
          }

          console.log(`解析第${i}行数据:`, workOrder)

          // 只添加有效的工单数据
          if (workOrder.storeName && workOrder.description) {
            excelData.push(workOrder)
          }
        }

        console.log('最终解析的Excel数据:', excelData)
        resolve(excelData)
      } catch (error) {
        console.error('Excel解析错误:', error)
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(file)
  })
}

export default function FileUploadPage() {
  const [file, setFile] = useState<File | null>(null)
  const [submitMode, setSubmitMode] = useState<"direct" | "batch">("direct")
  const [batchSize] = useState(4) // 每批处理4个工单
  const [isDragOver, setIsDragOver] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [fileError, setFileError] = useState("")
  const submitSectionRef = useRef<HTMLDivElement>(null)
  const resultsSectionRef = useRef<HTMLDivElement>(null)

  const [showLoginModal, setShowLoginModal] = useState(false)
  const [phone, setPhone] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [currentUser, setCurrentUser] = useState<UserCredentials | null>(null)
  const [isLoggingIn, setIsLoggingIn] = useState(false)
  const [currentPassword, setCurrentPassword] = useState<string>("")
  const [submissionResults, setSubmissionResults] = useState<SubmissionResult[]>([])
  const [showResults, setShowResults] = useState(false)
  const [showTaskHistory, setShowTaskHistory] = useState(false)
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null)
  const [batchTasks, setBatchTasks] = useState<BatchTask[]>([
    {
      id: "1",
      taskId: "#2b597514",
      date: "2025-08-21",
      progress: 100,
      completed: 2,
      total: 2,
      status: "completed",
    },
    {
      id: "2",
      taskId: "#e91a37a9",
      date: "2025-08-13",
      progress: 100,
      completed: 2,
      total: 2,
      status: "completed",
    },
    {
      id: "3",
      taskId: "#2c921b5e",
      date: "2025-08-11",
      progress: 100,
      completed: 60,
      total: 60,
      status: "completed",
    },
    {
      id: "4",
      taskId: "#d7b662e9",
      date: "2025-08-11",
      progress: 100,
      completed: 0,
      total: 60,
      status: "failed",
    },
    {
      id: "5",
      taskId: "#fc32670e",
      date: "2025-08-10",
      progress: 100,
      completed: 25,
      total: 25,
      status: "completed",
    },
  ])

  const [loginError, setLoginError] = useState<string>("")

  // 初始化时清除任何保存的用户状态
  useEffect(() => {
    const clearUserStatus = async () => {
      try {
        // 清除任何保存的用户凭证
        await TauriApiClient.logout()
        setCurrentUser(null)
        setIsLoggedIn(false)
        setCurrentPassword("")
      } catch (error) {
        console.error('清除用户状态失败:', error)
      }
    }

    clearUserStatus()
  }, [])

  const validateFile = (file: File): string => {
    const maxSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = [
      "text/csv",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/json",
    ]

    if (file.size > maxSize) {
      return "文件大小不能超过 10MB"
    }

    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(csv|xlsx|xls|json)$/i)) {
      return "仅支持 CSV、Excel 和 JSON 格式文件"
    }

    return ""
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    const files = e.dataTransfer.files
    if (files.length > 0) {
      const error = validateFile(files[0])
      if (error) {
        setFileError(error)
        return
      }
      setFileError("")
      setFile(files[0])

      // 文件拖拽成功后，延迟滚动到提交区域
      setTimeout(() => {
        submitSectionRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }, 300)
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      const error = validateFile(files[0])
      if (error) {
        setFileError(error)
        return
      }
      setFileError("")
      setFile(files[0])

      // 文件选择成功后，延迟滚动到提交区域
      setTimeout(() => {
        submitSectionRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }, 300)
    }
  }

  const handleUploadAreaClick = () => {
    const fileInput = document.getElementById("file-upload") as HTMLInputElement
    if (fileInput) {
      fileInput.click()
    }
  }

  const handleSubmit = async () => {
    if (!file) return

    // 每次点击提交都弹出登录窗口
    setShowLoginModal(true)
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoginError("")
    setIsLoggingIn(true)

    if (!phone || !password) {
      setLoginError("请填写完整的登录信息")
      setIsLoggingIn(false)
      return
    }

    // 简单的手机号验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      setLoginError("请输入有效的手机号")
      setIsLoggingIn(false)
      return
    }

    if (password.length < 6) {
      setLoginError("密码长度至少6位")
      setIsLoggingIn(false)
      return
    }

    try {
      // 调用登录API（支持浏览器和Tauri环境）
      const loginResult = await TauriApiClient.login(phone, password)

      // 创建用户凭证对象
      const userCredentials = {
        token: loginResult.token,
        company_id: loginResult.company_id,
        user_id: loginResult.user_id,
        username: phone,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24小时后过期
      }

      // 在浏览器环境中保存到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('user_credentials', JSON.stringify(userCredentials))
      }

      // 设置当前用户状态（仅用于本次提交）
      setCurrentUser(userCredentials)
      setShowLoginModal(false)
      setCurrentPassword(password) // 保存密码供后续API调用使用
      setPhone("")
      setPassword("")
      setLoginError("")

      // 登录成功后立即处理提交，直接传递用户凭证和密码
      await handleSubmitAfterLogin(userCredentials, password)

      // 提交完成后清除用户状态，确保下次提交时重新登录
      setCurrentUser(null)
      setCurrentPassword("")
    } catch (error) {
      console.error('登录失败:', error)
      setLoginError(error instanceof Error ? error.message : '登录失败，请检查用户名和密码')
    } finally {
      setIsLoggingIn(false)
    }
  }

  const handleLogout = async () => {
    try {
      await TauriApiClient.logout()
      setCurrentUser(null)
      setIsLoggedIn(false)
      setCurrentPassword("")
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  // 随机延迟函数
  const randomDelay = (min: number, max: number) => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min
    console.log(`⏱️ 等待 ${delay} 秒...`)
    return new Promise(resolve => setTimeout(resolve, delay * 1000))
  }

  // 处理单个工单
  const processSingleWorkOrder = async (
    workOrder: ExcelWorkOrder,
    user: UserCredentials,
    password: string,
    index: number
  ): Promise<SubmissionResult> => {
    try {
      // 查询门店获取客户ID
      console.log(`🏪 查询门店: ${workOrder.storeName}`)
      const storeResult = await TauriApiClient.queryStore({
        customer_name: workOrder.storeName,
        username: user.username,
        password: password,
        company_id: user.company_id
      })
      console.log(`✅ 门店查询成功:`, storeResult)

      // 提交工单
      console.log(`📝 提交工单: ${workOrder.storeName}`)
      const submitResult = await TauriApiClient.submitWorkOrder({
        username: user.username,
        password: password,
        company_id: user.company_id,
        customer_id: storeResult.customer_id,
        customer_name: storeResult.customer_name,
        description: workOrder.description,
        result: workOrder.result || "已处理完成",
        completion_time: workOrder.completion_time || new Date().toISOString().slice(0, 19).replace('T', ' '),
        report_time: workOrder.report_time || new Date().toISOString().slice(0, 19).replace('T', ' '),
        is_remote: workOrder.is_remote || 0,
        has_sop: workOrder.has_sop || 1,
        sop_description: workOrder.sop_description || "按标准流程处理"
      })
      console.log(`✅ 工单提交成功:`, submitResult)

      return {
        id: submitResult.work_order_id,
        storeName: workOrder.storeName,
        description: workOrder.description,
        status: "success",
        timestamp: new Date(),
      }
    } catch (error) {
      console.error(`❌ 处理失败: ${workOrder.storeName}`, error)
      return {
        id: `error-${index}`,
        storeName: workOrder.storeName,
        description: workOrder.description,
        status: "error",
        timestamp: new Date(),
        error: error instanceof Error ? error.message : '处理失败'
      }
    }
  }

  // 直接提交模式 - 顺序处理（避免API并发限制）
  const processDirectSubmit = async (
    excelData: ExcelWorkOrder[],
    user: UserCredentials,
    password: string,
    results: SubmissionResult[]
  ) => {
    console.log("🚀 直接提交模式：顺序处理所有工单")

    for (let i = 0; i < excelData.length; i++) {
      const workOrder = excelData[i]
      console.log(`🔄 处理第${i + 1}条数据: ${workOrder.storeName}`)

      const result = await processSingleWorkOrder(workOrder, user, password, i)
      results.push(result)

      // 添加短暂延迟避免API限流（比分批提交更短）
      if (i < excelData.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000)) // 2秒延迟
      }
    }
  }

  // 分批提交模式 - 后台处理
  const processBatchSubmitBackground = async (
    excelData: ExcelWorkOrder[],
    user: UserCredentials,
    password: string,
    taskId: string,
    batchSize: number
  ) => {
    console.log(`📦 后台分批提交模式：每批 ${batchSize} 个工单`)

    const totalBatches = Math.ceil(excelData.length / batchSize)
    const results: SubmissionResult[] = []

    // 更新任务状态为处理中
    setBatchTasks(prev => prev.map(task =>
      task.id === taskId
        ? {
            ...task,
            status: "processing",
            startTime: new Date(),
            totalBatches,
            currentBatch: 1,
            batchSize
          }
        : task
    ))

    try {
      for (let i = 0; i < excelData.length; i += batchSize) {
        const batch = excelData.slice(i, i + batchSize)
        const batchNumber = Math.floor(i / batchSize) + 1

        console.log(`📦 处理第 ${batchNumber}/${totalBatches} 批，共 ${batch.length} 个工单`)

        // 更新当前批次
        setBatchTasks(prev => prev.map(task =>
          task.id === taskId
            ? { ...task, currentBatch: batchNumber }
            : task
        ))

        // 处理当前批次的工单
        for (let j = 0; j < batch.length; j++) {
          const workOrder = batch[j]
          const globalIndex = i + j

          console.log(`🔄 处理第${globalIndex + 1}条数据: ${workOrder.storeName}`)
          const result = await processSingleWorkOrder(workOrder, user, password, globalIndex)
          results.push(result)

          // 更新进度
          const completed = globalIndex + 1
          const progress = Math.round((completed / excelData.length) * 100)

          setBatchTasks(prev => prev.map(task =>
            task.id === taskId
              ? {
                  ...task,
                  completed,
                  progress,
                  details: [...(task.details || []), result]
                }
              : task
          ))

          // 工单间随机延迟 10-30秒
          if (j < batch.length - 1) {
            await randomDelay(10, 30)
          }
        }

        // 批次间随机延迟 30-70秒（除了最后一批）
        if (i + batchSize < excelData.length) {
          console.log(`📦 第 ${batchNumber} 批处理完成，准备处理下一批...`)
          await randomDelay(30, 70)
        }
      }

      // 任务完成
      setBatchTasks(prev => prev.map(task =>
        task.id === taskId
          ? {
              ...task,
              status: "completed",
              progress: 100,
              completed: excelData.length
            }
          : task
      ))

      console.log("🎉 分批提交任务完成!")

    } catch (error) {
      console.error("💥 分批提交任务失败:", error)

      // 任务失败
      setBatchTasks(prev => prev.map(task =>
        task.id === taskId
          ? { ...task, status: "failed" }
          : task
      ))
    }
  }




  const handleSubmitAfterLogin = async (userCredentials?: UserCredentials, userPassword?: string) => {
    // 使用传入的参数或当前状态
    const user = userCredentials || currentUser
    const password = userPassword || currentPassword

    if (!file || !user) {
      console.error("❌ 缺少必要参数:", { file: !!file, user: !!user })
      return
    }

    setIsSubmitting(true)
    const results: SubmissionResult[] = []

    try {
      console.log("📊 开始处理Excel文件...")
      console.log("👤 使用用户:", { username: user.username, company_id: user.company_id })

      // 1. 解析Excel文件
      const excelData = await parseExcelFile(file)
      console.log("✅ Excel解析完成，共", excelData.length, "条数据")

      // 2. 根据提交模式处理数据
      if (submitMode === "direct") {
        console.log("🚀 使用直接提交模式")
        await processDirectSubmit(excelData, user, password, results)

        // 直接提交完成后显示结果
        setSubmissionResults(results)
        setShowResults(true)

        // 滚动到页面顶部
        setTimeout(() => {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          })
        }, 500)

      } else {
        console.log("📦 使用分批提交模式 - 后台处理")

        // 创建新的批次任务
        const newTaskId = `batch_${Date.now()}`
        const newTask: BatchTask = {
          id: newTaskId,
          taskId: `#${newTaskId.slice(-8)}`,
          date: new Date().toISOString().slice(0, 10),
          progress: 0,
          completed: 0,
          total: excelData.length,
          status: "pending",
          currentBatch: 0,
          totalBatches: Math.ceil(excelData.length / batchSize),
          batchSize,
          startTime: new Date(),
          details: []
        }

        // 添加到任务列表
        setBatchTasks(prev => [newTask, ...prev])
        setCurrentTaskId(newTaskId)

        // 立即跳转到任务历史
        setShowTaskHistory(true)

        // 后台开始处理
        processBatchSubmitBackground(excelData, user, password, newTaskId, batchSize)

        // 不等待完成，直接返回
        setIsSubmitting(false)
        return
      }

      console.log("🎉 直接提交处理完成!")

    } catch (error) {
      console.error("💥 处理过程中出现错误:", error)
      alert(`处理失败: ${error}`)
      setSubmissionResults(results)
      setShowResults(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const downloadTemplate = () => {
    const csvContent = "姓名,邮箱,电话\n张三,<EMAIL>,13800138000\n李四,<EMAIL>,13900139000"
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", "数据模板.csv")
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (showTaskHistory) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 p-4">
        <div className="max-w-4xl mx-auto space-y-8">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                任务历史
              </h1>
              <p className="text-muted-foreground">查看所有批量任务的执行记录</p>
            </div>
            <Button
              onClick={() => setShowTaskHistory(false)}
              variant="outline"
              className="bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-all duration-200"
            >
              返回上传页面
            </Button>
          </div>

          <Card className="shadow-xl border-0 bg-card/80 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <History className="h-5 w-5 text-primary" />
                批量任务历史
              </CardTitle>
              <CardDescription>共 {batchTasks.length} 个任务记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {batchTasks.map((task) => (
                <div
                  key={task.id}
                  className="group flex items-center gap-4 p-4 bg-background/50 rounded-xl border border-border/50 hover:bg-background/80 hover:border-border hover:shadow-md transition-all duration-200 cursor-pointer"
                >
                  <div className="flex-shrink-0">
                    {task.status === "failed" ? (
                      <div className="relative">
                        <X className="h-6 w-6 text-red-600 bg-red-100 dark:bg-red-950/30 rounded-full p-1" />
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                      </div>
                    ) : task.status === "processing" ? (
                      <div className="relative">
                        <Loader2 className="h-6 w-6 text-blue-600 bg-blue-100 dark:bg-blue-950/30 rounded-full p-1 animate-spin" />
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
                      </div>
                    ) : task.status === "pending" ? (
                      <Clock className="h-6 w-6 text-yellow-600 bg-yellow-100 dark:bg-yellow-950/30 rounded-full p-1" />
                    ) : (
                      <CheckCircle className="h-6 w-6 text-green-600 bg-green-100 dark:bg-green-950/30 rounded-full p-1" />
                    )}
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="font-medium text-foreground group-hover:text-primary transition-colors">
                      批量任务 {task.taskId}
                      {task.status === "processing" && (
                        <span className="ml-2 text-xs text-blue-600 font-normal">
                          正在处理第 {task.currentBatch}/{task.totalBatches} 批
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                        {task.date}
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                        {task.progress}%
                      </span>
                      <span className="flex items-center gap-1">
                        <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                        {task.completed}/{task.total} 已完成
                      </span>
                      {task.totalBatches && (
                        <span className="flex items-center gap-1">
                          <div className="w-1 h-1 bg-muted-foreground rounded-full" />
                          共 {task.totalBatches} 批
                        </span>
                      )}
                    </div>
                    {task.status === "processing" && task.progress > 0 && (
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                  <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 p-4">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-3">
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            文件上传
          </h1>
          <p className="text-muted-foreground text-lg">选择文件并配置提交方式，支持多种格式</p>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={() => setShowTaskHistory(true)}
            variant="outline"
            className="bg-background/80 backdrop-blur-sm hover:bg-background/90 flex items-center gap-2 transition-all duration-200"
          >
            <History className="h-4 w-4" />
            任务历史
          </Button>
        </div>

        {showResults && (
          <Card
            ref={resultsSectionRef}
            className="shadow-xl border-0 bg-card/80 backdrop-blur-sm animate-in slide-in-from-top-4 duration-500"
          >
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-green-600 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                提交成功
              </CardTitle>
              <CardDescription>以下 {submissionResults.length} 条数据已成功处理</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="max-h-96 overflow-y-auto space-y-3 pr-2">
              {submissionResults.map((result, index) => (
                <div
                  key={`result-${index}-${result.id}`}
                  className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-950/20 rounded-xl border border-green-200 dark:border-green-800 animate-in slide-in-from-left-4 duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="font-medium text-foreground">店名: {result.storeName}</div>
                    <div className="text-sm text-muted-foreground mt-1">故障描述: {result.description}</div>
                  </div>
                </div>
              ))}
              </div>
              <div className="flex gap-4 pt-4">
                <Button
                  onClick={() => {
                    setShowResults(false)
                    setFile(null)
                    setSubmissionResults([])
                    setFileError("")
                  }}
                  variant="outline"
                  className="bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-all duration-200"
                >
                  继续上传
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="shadow-xl border-0 bg-card/80 backdrop-blur-sm">
          <CardContent className="p-8">
            <div
              className={`border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 cursor-pointer ${
                isDragOver
                  ? "border-primary bg-primary/10 scale-[1.02]"
                  : fileError
                    ? "border-red-300 bg-red-50 dark:bg-red-950/10"
                    : "border-border hover:border-primary/50 hover:bg-muted/30 hover:scale-[1.01]"
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleUploadAreaClick}
            >
              <div className="space-y-4">
                <div
                  className={`w-16 h-16 mx-auto rounded-2xl flex items-center justify-center transition-all duration-300 ${
                    fileError ? "bg-red-100 dark:bg-red-950/20" : "bg-primary/10"
                  }`}
                >
                  {fileError ? (
                    <AlertCircle className="w-8 h-8 text-red-600" />
                  ) : (
                    <Upload className="w-8 h-8 text-primary" />
                  )}
                </div>

                {fileError && (
                  <div className="text-red-600 font-medium animate-in slide-in-from-top-2 duration-300">
                    {fileError}
                  </div>
                )}

                {file && !fileError ? (
                  <div className="space-y-3 animate-in slide-in-from-bottom-4 duration-300">
                    <div className="flex items-center justify-center gap-2">
                      <FileText className="h-5 w-5 text-primary" />
                      <p className="text-lg font-medium text-foreground">{file.name}</p>
                    </div>
                    <p className="text-sm text-muted-foreground">大小: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <div className="inline-flex items-center gap-2 px-3 py-1 bg-green-100 dark:bg-green-950/20 text-green-700 dark:text-green-400 rounded-full text-sm">
                      <CheckCircle className="h-3 w-3" />
                      文件已选择
                    </div>
                  </div>
                ) : (
                  !fileError && (
                    <div className="space-y-3">
                      <p className="text-lg font-medium text-foreground">拖拽文件到此处或点击选择</p>
                      <p className="text-sm text-muted-foreground">支持 CSV、Excel 和 JSON 格式，最大 10MB</p>
                    </div>
                  )
                )}

                <input
                  type="file"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                  accept=".csv,.xlsx,.xls,.json"
                />
                <Button
                  variant="outline"
                  className="bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-all duration-200 pointer-events-none"
                  onClick={(e) => e.stopPropagation()}
                >
                  {file ? "重新选择" : "选择文件"}
                </Button>
              </div>
            </div>


          </CardContent>
        </Card>

        {/* 提交模块 - 只在文件上传后显示 */}
        {file && !fileError && (
          <Card
            ref={submitSectionRef}
            className="shadow-xl border-0 bg-card/80 backdrop-blur-sm mt-8 animate-in slide-in-from-bottom-4 duration-500"
          >
            <CardContent className="p-8">
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">提交方式</h3>
                  <p className="text-sm text-muted-foreground">选择适合您文件大小的提交方式</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card
                    className={`cursor-pointer transition-all duration-300 hover:scale-[1.02] ${
                      submitMode === "direct"
                        ? "ring-2 ring-primary bg-primary/5 shadow-lg"
                        : "hover:bg-muted/30 hover:shadow-md"
                    }`}
                    onClick={() => setSubmitMode("direct")}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-3">
                        <div
                          className={`w-5 h-5 rounded-full border-2 mt-0.5 transition-all duration-200 ${
                            submitMode === "direct"
                              ? "bg-primary border-primary shadow-sm"
                              : "border-muted-foreground hover:border-primary"
                          }`}
                        />
                        <div className="space-y-2">
                          <h4 className="font-medium">直接提交</h4>
                          <p className="text-sm text-muted-foreground">一次性提交所有工单</p>
                          <div className="text-xs text-primary font-medium">推荐用于快速处理</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card
                    className={`cursor-pointer transition-all duration-300 hover:scale-[1.02] ${
                      submitMode === "batch"
                        ? "ring-2 ring-primary bg-primary/5 shadow-lg"
                        : "hover:bg-muted/30 hover:shadow-md"
                    }`}
                    onClick={() => setSubmitMode("batch")}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-3">
                        <div
                          className={`w-5 h-5 rounded-full border-2 mt-0.5 transition-all duration-200 ${
                            submitMode === "batch"
                              ? "bg-primary border-primary shadow-sm"
                              : "border-muted-foreground hover:border-primary"
                          }`}
                        />
                        <div className="space-y-2">
                          <h4 className="font-medium">分批提交</h4>
                          <p className="text-sm text-muted-foreground">分批处理，更稳定可靠</p>
                          <div className="text-xs text-primary font-medium space-y-1">
                            <div>• 每批3-5个工单</div>
                            <div>• 每个工单提交间隔: 10-30秒随机延迟</div>
                            <div>• 每批次间隔: 30-70秒随机延迟</div>
                            <div>• 提交后可在提交历史查看进度</div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    onClick={handleSubmit}
                    disabled={!file || isSubmitting || !!fileError}
                    className="flex-1 h-12 bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-xl transition-all duration-200 shadow-sm hover:shadow-lg hover:scale-[1.02] disabled:hover:scale-100"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        提交中...
                      </div>
                    ) : (
                      "开始提交"
                    )}
                  </Button>
                  <Button
                    onClick={downloadTemplate}
                    variant="outline"
                    className="h-12 px-8 font-medium rounded-xl transition-all duration-200 bg-background/80 backdrop-blur-sm hover:bg-background/90 hover:scale-[1.02]"
                  >
                    下载模板
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {showLoginModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50 animate-in fade-in duration-300">
          <Card className="w-full max-w-md shadow-2xl border-0 bg-card/95 backdrop-blur-sm animate-in zoom-in-95 duration-300">
            <CardHeader className="space-y-1 text-center pb-6 relative">
              <button
                onClick={() => {
                  setShowLoginModal(false)
                  setLoginError("")
                }}
                className="absolute right-4 top-4 text-muted-foreground hover:text-foreground transition-colors rounded-full p-1 hover:bg-muted/50"
              >
                <X className="h-5 w-5" />
              </button>
              <CardTitle className="text-2xl font-bold tracking-tight">登录验证</CardTitle>
              <CardDescription className="text-muted-foreground">请输入您的账户信息以继续提交文件</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-6">
                {loginError && (
                  <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg text-red-700 dark:text-red-400 text-sm animate-in slide-in-from-top-2 duration-300">
                    <AlertCircle className="h-4 w-4 flex-shrink-0" />
                    {loginError}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="modal-phone" className="text-sm font-medium">
                    手机号
                  </Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      id="modal-phone"
                      type="tel"
                      placeholder="请输入手机号"
                      value={phone}
                      onChange={(e) => {
                        setPhone(e.target.value)
                        setLoginError("")
                      }}
                      className="pl-10 h-12 bg-background/50 border-border focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 rounded-lg"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="modal-password" className="text-sm font-medium">
                    密码
                  </Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      id="modal-password"
                      type={showPassword ? "text" : "password"}
                      placeholder="请输入密码"
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value)
                        setLoginError("")
                      }}
                      className="pl-10 pr-10 h-12 bg-background/50 border-border focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 rounded-lg"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors rounded p-1"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isLoggingIn}
                  className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-xl transition-all duration-200 shadow-sm hover:shadow-lg disabled:opacity-50"
                >
                  {isLoggingIn ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      登录中...
                    </>
                  ) : (
                    "登录并提交"
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
