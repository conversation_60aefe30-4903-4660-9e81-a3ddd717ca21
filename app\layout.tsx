import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Space_Grotesk } from "next/font/google"
import "./globals.css"

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-sans",
})

export const metadata: Metadata = {
  title: "ldx",
  description: "登录和文件上传应用",
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN" className={`${spaceGrotesk.variable} antialiased`}>
      <body>{children}</body>
    </html>
  )
}
