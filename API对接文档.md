# 卤蛋侠工单系统 - API对接文档

## 项目概述
卤蛋侠工单系统是一个批量工单上传和管理系统，主要与 `https://kf.uhi-networks.com` 域名进行API对接。

## API基础信息

### 基础URL
```
https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api
```

### 请求格式
所有API请求都使用POST方法，请求体格式为：
```json
{
  "header": {
    "code": "API_CODE"
  },
  "body": {
    // 具体参数
  }
}
```

### 响应格式
```json
{
  "header": {
    "errorMessage": "错误信息"
  },
  "body": {
    "isSuccessful": true/false,
    "resultData": {
      // 具体数据
    }
  }
}
```

## API接口详细说明

### 1. 用户登录 (HXCS_APP_SJDL)

**功能**: 用户身份验证和获取访问令牌

**API代码**: `HXCS_APP_SJDL`

**请求参数**:
```json
{
  "header": {
    "code": "HXCS_APP_SJDL"
  },
  "body": {
    "companyId": null,
    "token": "",
    "userName": "用户名",
    "passWord": "密码",
    "deviceId": "设备ID(通常使用用户名)",
    "language": "zh",
    "osType": "2"
  }
}
```

**响应数据**:
```json
{
  "body": {
    "isSuccessful": true,
    "resultData": {
      "token": "访问令牌",
      "companyId": "公司ID",
      "userId": "用户ID"
    }
  }
}
```

**完整请求示例**:
```bash
curl -X POST https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "code": "HXCS_APP_SJDL"
    },
    "body": {
      "companyId": null,
      "token": "",
      "userName": "your_username",
      "passWord": "your_password",
      "deviceId": "your_username",
      "language": "zh",
      "osType": "2"
    }
  }'
```

**JavaScript请求示例**:
```javascript
const login = async (username, password) => {
  const response = await axios.post(
    'https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api',
    {
      header: {
        code: 'HXCS_APP_SJDL'
      },
      body: {
        companyId: null,
        token: '',
        userName: username,
        passWord: password,
        deviceId: username,
        language: 'zh',
        osType: '2'
      }
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  )
  return response.data
}
```

**项目中的实现**:
- 文件: `src/utils/apiService.js` (login函数)
- 文件: `src/views/LoginView.vue` (handleLogin函数)

### 2. 门店信息查询 (HXCS_ZAPP_MDLB)

**功能**: 根据门店名称查询门店信息，获取门店ID

**API代码**: `HXCS_ZAPP_MDLB`

**请求参数**:
```json
{
  "header": {
    "code": "HXCS_ZAPP_MDLB"
  },
  "body": {
    "companyId": "公司ID",
    "token": "访问令牌",
    "customerName": "门店名称"
  }
}
```

**响应数据**:
```json
{
  "body": {
    "isSuccessful": true,
    "resultData": {
      "rows": [
        {
          "id": "门店ID",
          "name": "门店名称"
        }
      ]
    }
  }
}
```

**完整请求示例**:
```bash
curl -X POST https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "code": "HXCS_ZAPP_MDLB"
    },
    "body": {
      "companyId": "your_company_id",
      "token": "your_access_token",
      "customerName": "北京朝阳店"
    }
  }'
```

**JavaScript请求示例**:
```javascript
const getStoreInfo = async (customerName) => {
  const token = localStorage.getItem('token')
  const companyId = localStorage.getItem('companyId')

  const response = await axios.post(
    'https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api',
    {
      header: {
        code: 'HXCS_ZAPP_MDLB'
      },
      body: {
        companyId,
        token,
        customerName
      }
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  )
  return response.data
}
```

**项目中的实现**:
- 文件: `src/utils/apiService.js` (getStoreInfo函数)
- 文件: `src/views/UploadView.vue` (getStoreInfo函数)
- 文件: `server/src/services/taskService.ts` (getStoreInfo函数)

### 3. 工单提交 (HXCS_ZAPP_FWGDTB)

**功能**: 提交服务工单

**API代码**: `HXCS_ZAPP_FWGDTB`

**请求参数**:
```json
{
  "header": {
    "code": "HXCS_ZAPP_FWGDTB"
  },
  "body": {
    "companyId": "公司ID",
    "token": "访问令牌",
    "customerId": "门店ID",
    "customerName": "门店名称",
    "affectRangeId": "20191126b3da41b498b675b16ced3472",
    "affectRangeName": "部分功能",
    "serverTypeId": "20191126b0214f0faba42f696abadbd8",
    "serverTypeName": "例行维护",
    "malfunctionTypeId": "20201216b38b479f86def7bf47ca922d",
    "malfunctionTypeName": "海底捞(故障)",
    "processesRemark": "处理结果",
    "finishTime": "完成时间",
    "repairsTimes": "报修时间",
    "isLong": "是否远程(0是/1否)",
    "processesUrls": "",
    "faultFileUrls": "",
    "faultRemark": "故障描述",
    "fromType": "5",
    "isThereAnSop": "是否有SOP(1是/0否)",
    "sopDescription": "SOP流程描述(可选)"
  }
}
```

**完整请求示例**:
```bash
curl -X POST https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "code": "HXCS_ZAPP_FWGDTB"
    },
    "body": {
      "companyId": "your_company_id",
      "token": "your_access_token",
      "customerId": "store_id_from_query",
      "customerName": "北京朝阳店",
      "affectRangeId": "20191126b3da41b498b675b16ced3472",
      "affectRangeName": "部分功能",
      "serverTypeId": "20191126b0214f0faba42f696abadbd8",
      "serverTypeName": "例行维护",
      "malfunctionTypeId": "20201216b38b479f86def7bf47ca922d",
      "malfunctionTypeName": "海底捞(故障)",
      "processesRemark": "重启服务器解决问题",
      "finishTime": "2024-01-15 14:30:00",
      "repairsTimes": "2024-01-15 10:00:00",
      "isLong": 1,
      "processesUrls": "",
      "faultFileUrls": "",
      "faultRemark": "收银系统无法启动",
      "fromType": "5",
      "isThereAnSop": 0
    }
  }'
```

**带SOP的请求示例**:
```bash
curl -X POST https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "code": "HXCS_ZAPP_FWGDTB"
    },
    "body": {
      "companyId": "your_company_id",
      "token": "your_access_token",
      "customerId": "store_id_from_query",
      "customerName": "北京朝阳店",
      "affectRangeId": "20191126b3da41b498b675b16ced3472",
      "affectRangeName": "部分功能",
      "serverTypeId": "20191126b0214f0faba42f696abadbd8",
      "serverTypeName": "例行维护",
      "malfunctionTypeId": "20201216b38b479f86def7bf47ca922d",
      "malfunctionTypeName": "海底捞(故障)",
      "processesRemark": "按照SOP流程重启系统",
      "finishTime": "2024-01-15 14:30:00",
      "repairsTimes": "2024-01-15 10:00:00",
      "isLong": 0,
      "processesUrls": "",
      "faultFileUrls": "",
      "faultRemark": "收银系统无法启动",
      "fromType": "5",
      "isThereAnSop": 1,
      "sopDescription": "按照标准流程重启系统，检查网络连接"
    }
  }'
```

**JavaScript请求示例**:
```javascript
// 工单提交请求
const submitWorkOrder = async (workOrderData) => {
  const token = localStorage.getItem('token')
  const companyId = localStorage.getItem('companyId')

  const requestBody = {
    header: {
      code: 'HXCS_ZAPP_FWGDTB'
    },
    body: {
      companyId,
      token,
      customerId: workOrderData.customerId,
      customerName: workOrderData.customerName,
      affectRangeId: '20191126b3da41b498b675b16ced3472',
      affectRangeName: '部分功能',
      serverTypeId: '20191126b0214f0faba42f696abadbd8',
      serverTypeName: '例行维护',
      malfunctionTypeId: '20201216b38b479f86def7bf47ca922d',
      malfunctionTypeName: '海底捞(故障)',
      processesRemark: workOrderData.处理结果,
      finishTime: workOrderData['完成时间（注意格式）'],
      repairsTimes: workOrderData['报修时间（注意格式）'],
      isLong: workOrderData['是否远程(0是/1否)'],
      processesUrls: '',
      faultFileUrls: '',
      faultRemark: workOrderData.故障描述,
      fromType: '5',
      isThereAnSop: workOrderData['是否有sop(1是/0否)']
    }
  }

  // 只有当isThereAnSop为1时，才添加sopDescription字段
  if (workOrderData['是否有sop(1是/0否)'] === 1) {
    requestBody.body.sopDescription = workOrderData.sop流程描述
  }

  const response = await axios.post(
    'https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api',
    requestBody,
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  )

  return response.data
}
```

**项目中的实现**:
- 文件: `src/utils/apiService.js` (submitWorkOrder函数)
- 文件: `src/views/UploadView.vue` (submitWorkOrder函数)
- 文件: `server/src/services/taskService.ts` (submitWorkOrder函数)

### 4. 工单列表查询 (HXCS_ZAPP_RWGDWCL)

**功能**: 查询工单列表

**API代码**: `HXCS_ZAPP_RWGDWCL`

**请求参数**:
```json
{
  "header": {
    "code": "HXCS_ZAPP_RWGDWCL"
  },
  "body": {
    "companyId": "公司ID",
    "token": "访问令牌",
    "page": 1,
    "pageSize": 50,
    "customerName": "",
    "startDate": "",
    "endDate": "",
    "flag": 1,
    "taskCode": ""
  }
}
```

**项目中的实现**:
- 文件: `src/views/TaskView.vue` (fetchWorkOrders函数)

### 5. 工单批量处理 (HXCS_ZAPP_RWGDBC)

**功能**: 批量处理工单状态

**API代码**: `HXCS_ZAPP_RWGDBC`

**请求参数**:
```json
{
  "header": {
    "code": "HXCS_ZAPP_RWGDBC"
  },
  "body": {
    "companyId": "公司ID",
    "token": "访问令牌",
    "costHour": "耗时小时",
    "costMin": "耗时分钟",
    "taskOrderIds": "工单ID列表(逗号分隔)",
    "state": "2",
    "taskOrderFileUrls": [],
    "taskOrderInventorys": [
      {
        "itemId": "20250427ce09429c97fd2908f72ccc8d",
        "itemName": "是否已完结",
        "state": "1",
        "itemOrderId": ""
      }
    ]
  }
}
```

**完整请求示例**:
```bash
curl -X POST https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" \
  -d '{
    "header": {
      "code": "HXCS_ZAPP_RWGDBC"
    },
    "body": {
      "companyId": "your_company_id",
      "token": "your_access_token",
      "costHour": "2",
      "costMin": "30",
      "taskOrderIds": "order_id_1,order_id_2,order_id_3",
      "state": "2",
      "taskOrderFileUrls": [],
      "taskOrderInventorys": [
        {
          "itemId": "20250427ce09429c97fd2908f72ccc8d",
          "itemName": "是否已完结",
          "state": "1",
          "itemOrderId": ""
        }
      ]
    }
  }'
```

**JavaScript请求示例**:
```javascript
const submitSelectedOrders = async (selectedOrderIds, costHour, costMin) => {
  const token = localStorage.getItem('token')
  const companyId = localStorage.getItem('companyId')

  const requestBody = {
    header: {
      code: 'HXCS_ZAPP_RWGDBC'
    },
    body: {
      companyId: companyId,
      token: token,
      costHour: costHour.toString(),
      costMin: costMin.toString(),
      taskOrderIds: selectedOrderIds.join(','),
      state: "2",
      taskOrderFileUrls: [],
      taskOrderInventorys: [
        {
          itemId: "20250427ce09429c97fd2908f72ccc8d",
          itemName: "是否已完结",
          state: "1",
          itemOrderId: ""
        }
      ]
    }
  }

  const response = await axios.post(
    'https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api',
    requestBody,
    {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      }
    }
  )

  return response.data
}
```

**项目中的实现**:
- 文件: `src/views/TaskView.vue` (submitSelectedOrders函数)

### 6. 本地任务管理API

**功能**: 项目内部的任务管理系统，用于批量处理工单

#### 6.1 创建批量任务 (POST /api/tasks)

**请求参数**:
```javascript
{
  workOrders: [
    {
      "报修用户": "门店名称",
      "故障描述": "故障描述",
      "处理结果": "处理结果",
      "完成时间（注意格式）": "2024-01-15 14:30:00",
      "报修时间（注意格式）": "2024-01-15 10:00:00",
      "是否远程(0是/1否)": 0,
      "是否有sop(1是/0否)": 1,
      "sop流程描述": "SOP描述"
    }
  ],
  batchSize: 10
}
```

**请求头**:
```javascript
{
  'X-User-Id': 'user_id',
  'X-Token': 'access_token',
  'X-Company-Id': 'company_id',
  'Content-Type': 'application/json'
}
```

#### 6.2 获取任务进度 (GET /api/tasks/:taskId)

**响应数据**:
```javascript
{
  success: true,
  data: {
    id: "task_id",
    status: "processing", // pending, processing, completed, failed
    progress: 75,
    completedCount: 15,
    failedCount: 2,
    totalCount: 20,
    currentBatch: 2,
    totalBatches: 2,
    items: [
      {
        id: "item_id",
        status: "completed",
        data: {
          customerName: "门店名称",
          faultRemark: "故障描述"
        },
        error: null
      }
    ],
    createdAt: "2024-01-15T10:00:00.000Z",
    updatedAt: "2024-01-15T10:30:00.000Z"
  }
}
```

#### 6.3 获取用户任务列表 (GET /api/tasks/user/:userId)

**响应数据**: 返回用户的所有任务列表，格式同任务进度接口

## Excel处理方式

### Excel文件解析
项目使用 `xlsx` 库来处理Excel文件：

**解析流程**:
1. 使用 `FileReader` 读取Excel文件
2. 使用 `XLSX.read()` 解析工作簿
3. 使用 `XLSX.utils.sheet_to_json()` 转换为JSON数据
4. 进行数据验证

**实现文件**:
- `src/utils/excelParser.js`
- `src/views/UploadView.vue` (parseExcel函数)

### Excel模板详细字段说明

| 列名 | 数据类型 | 是否必填 | 取值范围/格式 | 示例值 | 说明 |
|------|----------|----------|---------------|--------|------|
| 报修用户 | string | ✅ 必填 | 门店名称 | "北京朝阳店" | 必须是系统中存在的门店名称 |
| 故障描述 | string | ✅ 必填 | 文本描述 | "收银系统无法启动" | 详细描述故障现象 |
| 处理结果 | string | ✅ 必填 | 文本描述 | "重启服务器解决问题" | 详细描述处理过程和结果 |
| 完成时间（注意格式） | string | ✅ 必填 | YYYY-MM-DD HH:MM:SS | "2024-01-15 14:30:00" | 工单完成的具体时间 |
| 报修时间（注意格式） | string | ✅ 必填 | YYYY-MM-DD HH:MM:SS | "2024-01-15 10:00:00" | 故障报修的具体时间 |
| 是否远程(0是/1否) | number | ✅ 必填 | 0 或 1 | 0 | 0=远程处理，1=现场处理 |
| 是否有sop(1是/0否) | number | ✅ 必填 | 0 或 1 | 1 | 0=无SOP，1=有SOP |
| sop流程描述 | string | ❌ 可选 | 文本描述 | "按照标准流程重启系统" | 当"是否有sop"=1时建议填写 |

### Excel模板生成代码
```javascript
// Excel模板数据结构 (固定模板)
const EXCEL_TEMPLATE = {
  // 示例数据行 (固定示例值)
  SAMPLE_ROW: {
    '报修用户': '示例门店名称',                    // 固定示例值
    '故障描述': '示例故障描述',                    // 固定示例值
    '处理结果': '示例处理结果',                    // 固定示例值
    '完成时间（注意格式）': '2023-01-01 12:00:00', // 固定示例值
    '报修时间（注意格式）': '2023-01-01 10:00:00', // 固定示例值
    '是否远程(0是/1否)': 0,                      // 固定示例值
    '是否有sop(1是/0否)': 1,                     // 固定示例值
    'sop流程描述': '示例SOP流程描述'               // 固定示例值
  },

  // 列宽配置 (固定值)
  COLUMN_WIDTHS: [
    { wch: 20 }, // 报修用户
    { wch: 30 }, // 故障描述
    { wch: 30 }, // 处理结果
    { wch: 25 }, // 完成时间
    { wch: 25 }, // 报修时间
    { wch: 20 }, // 是否远程
    { wch: 20 }, // 是否有sop
    { wch: 30 }  // sop流程描述
  ],

  // 工作表名称 (固定值)
  SHEET_NAME: '工单模板',

  // 文件类型 (固定值)
  FILE_TYPE: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
}
```

### 数据验证规则详解
```javascript
// 必填字段配置 (固定值)
const REQUIRED_FIELDS = [
  '报修用户',
  '故障描述',
  '处理结果',
  '完成时间（注意格式）',
  '报修时间（注意格式）'
]

// 日期格式验证 (固定正则)
const DATE_PATTERN = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/

// 数值字段验证 (固定取值)
const NUMERIC_FIELD_VALIDATION = {
  '是否远程(0是/1否)': [0, 1],      // 只能是0或1
  '是否有sop(1是/0否)': [0, 1]      // 只能是0或1
}

// 字段长度限制 (固定值)
const FIELD_LENGTH_LIMITS = {
  '报修用户': { min: 1, max: 100 },
  '故障描述': { min: 1, max: 500 },
  '处理结果': { min: 1, max: 500 },
  'sop流程描述': { min: 0, max: 1000 }
}
```

## API参数与Excel列名对应关系

| Excel列名 | API参数名 | 说明 |
|-----------|-----------|------|
| 报修用户 | customerName | 门店名称，用于查询门店ID |
| 故障描述 | faultRemark | 故障描述信息 |
| 处理结果 | processesRemark | 处理结果描述 |
| 完成时间（注意格式） | finishTime | 格式：YYYY-MM-DD HH:MM:SS |
| 报修时间（注意格式） | repairsTimes | 格式：YYYY-MM-DD HH:MM:SS |
| 是否远程(0是/1否) | isLong | 0表示是，1表示否 |
| 是否有sop(1是/0否) | isThereAnSop | 1表示是，0表示否 |
| sop流程描述 | sopDescription | 当isThereAnSop=1时必填 |

## 详细参数说明

### 1. 用户登录参数详解 (HXCS_APP_SJDL)

| 参数名 | 类型 | 说明 | 来源/固定值 |
|--------|------|------|-------------|
| companyId | null | 公司ID | **固定值**: null (登录时为空) |
| token | string | 访问令牌 | **固定值**: "" (登录时为空字符串) |
| userName | string | 用户名 | **变量**: 用户输入的登录账号 |
| passWord | string | 密码 | **变量**: 用户输入的登录密码 |
| deviceId | string | 设备ID | **变量**: 使用用户名作为设备ID |
| language | string | 语言 | **固定值**: "zh" (中文) |
| osType | string | 操作系统类型 | **固定值**: "2" (Web端标识) |

### 2. 门店信息查询参数详解 (HXCS_ZAPP_MDLB)

| 参数名 | 类型 | 说明 | 来源/固定值 |
|--------|------|------|-------------|
| companyId | string | 公司ID | **变量**: 从localStorage获取，登录后返回 |
| token | string | 访问令牌 | **变量**: 从localStorage获取，登录后返回 |
| customerName | string | 门店名称 | **变量**: Excel中的"报修用户"字段 |

### 3. 工单提交参数详解 (HXCS_ZAPP_FWGDTB)

| 参数名 | 类型 | 说明 | 来源/固定值 |
|--------|------|------|-------------|
| companyId | string | 公司ID | **变量**: 从localStorage获取 |
| token | string | 访问令牌 | **变量**: 从localStorage获取 |
| customerId | string | 门店ID | **变量**: 通过门店查询API获取 |
| customerName | string | 门店名称 | **变量**: Excel中的"报修用户"字段 |
| affectRangeId | string | 影响范围ID | **固定值**: "20191126b3da41b498b675b16ced3472" |
| affectRangeName | string | 影响范围名称 | **固定值**: "部分功能" |
| serverTypeId | string | 服务类型ID | **固定值**: "20191126b0214f0faba42f696abadbd8" |
| serverTypeName | string | 服务类型名称 | **固定值**: "例行维护" |
| malfunctionTypeId | string | 故障类型ID | **固定值**: "20201216b38b479f86def7bf47ca922d" |
| malfunctionTypeName | string | 故障类型名称 | **固定值**: "海底捞(故障)" |
| processesRemark | string | 处理结果 | **变量**: Excel中的"处理结果"字段 |
| finishTime | string | 完成时间 | **变量**: Excel中的"完成时间（注意格式）"字段 |
| repairsTimes | string | 报修时间 | **变量**: Excel中的"报修时间（注意格式）"字段 |
| isLong | number | 是否远程 | **变量**: Excel中的"是否远程(0是/1否)"字段 |
| processesUrls | string | 处理过程文件URL | **固定值**: "" (空字符串) |
| faultFileUrls | string | 故障文件URL | **固定值**: "" (空字符串) |
| faultRemark | string | 故障描述 | **变量**: Excel中的"故障描述"字段 |
| fromType | string | 来源类型 | **固定值**: "5" (批量上传标识) |
| isThereAnSop | number | 是否有SOP | **变量**: Excel中的"是否有sop(1是/0否)"字段 |
| sopDescription | string | SOP流程描述 | **变量**: Excel中的"sop流程描述"字段 (仅当isThereAnSop=1时添加) |

### 4. 工单列表查询参数详解 (HXCS_ZAPP_RWGDWCL)

| 参数名 | 类型 | 说明 | 来源/固定值 |
|--------|------|------|-------------|
| companyId | string | 公司ID | **变量**: 从localStorage获取 |
| token | string | 访问令牌 | **变量**: 从localStorage获取 |
| page | number | 页码 | **固定值**: 1 (默认第一页) |
| pageSize | number | 每页数量 | **固定值**: 50 (每页50条记录) |
| customerName | string | 门店名称筛选 | **固定值**: "" (空字符串，不筛选) |
| startDate | string | 开始日期 | **固定值**: "" (空字符串，不筛选) |
| endDate | string | 结束日期 | **固定值**: "" (空字符串，不筛选) |
| flag | number | 标志位 | **固定值**: 1 (查询标识) |
| taskCode | string | 任务代码 | **固定值**: "" (空字符串，不筛选) |

### 5. 工单批量处理参数详解 (HXCS_ZAPP_RWGDBC)

| 参数名 | 类型 | 说明 | 来源/固定值 |
|--------|------|------|-------------|
| companyId | string | 公司ID | **变量**: 从localStorage获取 |
| token | string | 访问令牌 | **变量**: 从localStorage获取 |
| costHour | string | 耗时小时 | **变量**: 用户输入的小时数 |
| costMin | string | 耗时分钟 | **变量**: 用户输入的分钟数 |
| taskOrderIds | string | 工单ID列表 | **变量**: 选中的工单ID，逗号分隔 |
| state | string | 状态 | **固定值**: "2" (完成状态) |
| taskOrderFileUrls | array | 任务文件URL列表 | **固定值**: [] (空数组) |
| taskOrderInventorys | array | 任务清单 | **固定值**: 见下表 |

#### taskOrderInventorys 固定结构:
```javascript
[
  {
    itemId: "20250427ce09429c97fd2908f72ccc8d",     // 固定值: 清单项ID
    itemName: "是否已完结",                        // 固定值: 清单项名称
    state: "1",                                   // 固定值: 完成状态
    itemOrderId: ""                               // 固定值: 空字符串
  }
]
```

## HTTP请求头配置

### 标准请求头
所有API请求都使用以下HTTP请求头：

| 请求头名称 | 值 | 说明 |
|-----------|-----|------|
| Content-Type | application/json | **固定值**: JSON格式请求 |
| User-Agent | Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 | **固定值**: 浏览器标识 (仅工单批量处理时使用) |

### 请求头实现代码
```javascript
// 标准请求头
const headers = {
  'Content-Type': 'application/json'
}

// 工单批量处理时的请求头
const batchHeaders = {
  'Content-Type': 'application/json',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}
```

## 业务逻辑固定值说明

### 工单分类固定配置
项目中使用的工单分类都是固定值，对应海底捞业务系统中的具体分类：

```javascript
// 影响范围配置 (固定值)
const AFFECT_RANGE = {
  id: '20191126b3da41b498b675b16ced3472',
  name: '部分功能'
}

// 服务类型配置 (固定值)
const SERVER_TYPE = {
  id: '20191126b0214f0faba42f696abadbd8',
  name: '例行维护'
}

// 故障类型配置 (固定值)
const MALFUNCTION_TYPE = {
  id: '20201216b38b479f86def7bf47ca922d',
  name: '海底捞(故障)'
}

// 来源类型 (固定值)
const FROM_TYPE = '5'  // 表示批量上传来源

// 工单状态 (固定值)
const ORDER_STATE = '2'  // 表示完成状态

// 清单项配置 (固定值)
const INVENTORY_ITEM = {
  itemId: '20250427ce09429c97fd2908f72ccc8d',
  itemName: '是否已完结',
  state: '1',
  itemOrderId: ''
}
```

### 时间格式要求
```javascript
// 日期时间格式 (固定格式)
const DATE_FORMAT = 'YYYY-MM-DD HH:MM:SS'

// 示例: '2024-01-15 14:30:00'
// 正则验证: /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
```

### 数值字段取值范围
```javascript
// 是否远程字段 (固定取值)
const IS_LONG_VALUES = {
  0: '是',    // 远程处理
  1: '否'     // 现场处理
}

// 是否有SOP字段 (固定取值)
const IS_THERE_AN_SOP_VALUES = {
  0: '否',    // 没有SOP
  1: '是'     // 有SOP
}

// 工单查询标志位 (固定值)
const QUERY_FLAG = 1

// 分页配置 (固定值)
const PAGINATION = {
  page: 1,        // 默认第一页
  pageSize: 50    // 每页50条
}
```

## 错误处理

### 统一错误处理机制
1. **网络错误**: 服务器未响应
2. **服务器错误**: HTTP状态码错误
3. **业务错误**: isSuccessful为false
4. **数据格式错误**: 响应格式不符合预期

### 错误处理实现
- 文件: `src/utils/apiService.js` (apiRequest函数)
- 文件: `src/utils/errorHandler.js`

## 批量处理机制

### 任务分批处理
- **批次大小**: 可配置，默认每批处理多个工单
- **延迟机制**: 
  - 每个工单提交间隔: 10-30秒随机延迟
  - 每批次间隔: 30-70秒随机延迟
- **状态跟踪**: 实时更新任务进度和状态

### 实现文件
- `server/src/services/taskService.ts`
- `src/services/taskService.js`



## 项目架构说明

### 前端架构
- **框架**: Vue 3 + Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **Excel处理**: xlsx库

### 后端架构
- **框架**: Node.js + TypeScript
- **数据存储**: 文件系统 (JSON格式)
- **任务调度**: 内存队列 + 文件持久化

### 目录结构
```
src/
├── api/           # API接口定义
├── components/    # Vue组件
├── services/      # 业务服务层
├── utils/         # 工具函数
├── views/         # 页面组件
└── router/        # 路由配置

server/
├── src/
│   ├── routes/    # 路由处理
│   ├── services/  # 业务逻辑
│   └── types/     # 类型定义
└── data/          # 数据存储
```

## 核心功能流程

### 1. 用户登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关

    U->>F: 输入用户名密码
    F->>A: POST /api (HXCS_APP_SJDL)
    A->>F: 返回token和companyId
    F->>F: 存储到localStorage
    F->>U: 跳转到上传页面
```

### 2. Excel上传处理流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant S as 后端服务
    participant A as API网关

    U->>F: 上传Excel文件
    F->>F: 解析Excel数据
    F->>F: 验证数据格式
    F->>S: 创建批量任务
    S->>S: 分批处理工单
    loop 每个工单
        S->>A: 查询门店信息 (HXCS_ZAPP_MDLB)
        A->>S: 返回门店ID
        S->>A: 提交工单 (HXCS_ZAPP_FWGDTB)
        A->>S: 返回提交结果
        S->>S: 更新任务状态
    end
    S->>F: 返回任务完成状态
```

### 3. 工单查询流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关

    U->>F: 访问任务页面
    F->>A: POST /api (HXCS_ZAPP_RWGDWCL)
    A->>F: 返回工单列表
    F->>U: 显示工单信息
```

### 4. 工单批量处理流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关

    U->>F: 选择工单并设置耗时
    F->>A: POST /api (HXCS_ZAPP_RWGDBC)
    A->>F: 返回处理结果
    F->>U: 显示处理成功
    F->>F: 刷新工单列表
```

### 5. 任务历史查看流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant S as 后端服务

    U->>F: 访问任务历史页面
    F->>S: GET /api/tasks/user/:userId
    S->>F: 返回用户任务列表
    F->>U: 显示任务历史

    U->>F: 查看具体任务详情
    F->>S: GET /api/tasks/:taskId
    S->>F: 返回任务详细进度
    F->>U: 显示任务详情和进度
```

## 完整业务流程说明

### 系统使用完整流程

#### 第一步：用户登录
1. 用户访问系统，自动跳转到登录页面 (`/login`)
2. 输入用户名和密码
3. 系统调用 `HXCS_APP_SJDL` API进行身份验证
4. 登录成功后，系统存储 `token`、`companyId`、`userId` 到 localStorage
5. 自动跳转到上传页面 (`/upload`)

#### 第二步：Excel文件上传和处理
1. 用户在上传页面选择或拖拽Excel文件
2. 系统解析Excel文件，验证数据格式
3. 用户设置批量处理参数（批次大小、提交方式）
4. 点击提交后，系统创建后台任务
5. 任务在后台异步处理，用户可以查看实时进度

#### 第三步：批量工单处理（后台自动执行）
1. 系统将Excel数据分批处理
2. 对每个工单：
   - 调用 `HXCS_ZAPP_MDLB` 查询门店信息获取门店ID
   - 调用 `HXCS_ZAPP_FWGDTB` 提交工单
   - 记录处理结果（成功/失败）
3. 批次间和工单间有随机延迟，避免API限流
4. 更新任务进度和状态

#### 第四步：工单查看和管理
1. 用户访问任务页面 (`/task`)
2. 系统调用 `HXCS_ZAPP_RWGDWCL` 获取工单列表
3. 用户可以查看工单状态、选择工单进行批量操作
4. 对选中的工单调用 `HXCS_ZAPP_RWGDBC` 进行批量处理

#### 第五步：任务历史查看
1. 用户访问任务历史页面 (`/task-history`)
2. 系统调用本地API获取用户的所有任务
3. 用户可以查看每个任务的详细进度和处理结果

### 路由和权限控制

#### 路由结构
```javascript
const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', component: LoginView },
  { path: '/upload', component: UploadView, meta: { requiresAuth: true } },
  { path: '/task', component: TaskView, meta: { requiresAuth: true } },
  { path: '/task-history', component: TaskHistoryView, meta: { requiresAuth: true } }
]
```

#### 权限验证机制
1. **基础认证**: 检查 localStorage 中的 `token`
2. **路由守卫**: 未登录用户自动跳转到登录页
3. **管理员权限**: 特定账号 (19583751050, 15989664941) 具有管理员权限

### 错误处理和用户体验

#### 错误处理策略
1. **网络错误**: 显示网络连接提示
2. **API错误**: 显示具体错误信息
3. **数据格式错误**: 提供详细的格式要求说明
4. **批量处理错误**: 记录失败项目，支持重新处理

#### 用户体验优化
1. **实时进度**: 显示任务处理进度和状态
2. **响应式设计**: 支持移动设备访问
3. **拖拽上传**: 支持文件拖拽上传
4. **模板下载**: 提供标准Excel模板
5. **批量操作**: 支持批量选择和处理工单

## 数据流转详解

### Excel数据转换过程
1. **文件读取**: FileReader.readAsArrayBuffer()
2. **Excel解析**: XLSX.read() + XLSX.utils.sheet_to_json()
3. **数据验证**: 检查必填字段和格式
4. **数据映射**: Excel列名 → API参数名
5. **批量提交**: 分批次异步处理

### API数据映射示例
```javascript
// Excel原始数据
const excelRow = {
  '报修用户': '北京朝阳店',
  '故障描述': '收银系统无法启动',
  '处理结果': '重启服务器解决问题',
  '完成时间（注意格式）': '2024-01-15 14:30:00',
  '报修时间（注意格式）': '2024-01-15 10:00:00',
  '是否远程(0是/1否)': 1,
  '是否有sop(1是/0否)': 0
}

// 转换为API参数
const apiParams = {
  customerName: '北京朝阳店',
  faultRemark: '收银系统无法启动',
  processesRemark: '重启服务器解决问题',
  finishTime: '2024-01-15 14:30:00',
  repairsTimes: '2024-01-15 10:00:00',
  isLong: 1,
  isThereAnSop: 0
  // sopDescription 字段仅在 isThereAnSop=1 时添加
}
```

## 性能优化策略

### 1. 批量处理优化
- **分批大小**: 可配置批次大小，避免单次请求过多
- **并发控制**: 串行处理避免API限流
- **错误重试**: 失败工单可重新处理
- **进度跟踪**: 实时显示处理进度

### 2. 延迟策略详细配置
```javascript
// 随机延迟函数
const randomDelay = (minSeconds, maxSeconds) => {
  const delayMs = (Math.floor(Math.random() * (maxSeconds - minSeconds + 1)) + minSeconds) * 1000;
  return new Promise(resolve => setTimeout(resolve, delayMs));
};

// 延迟配置 (固定值)
const DELAY_CONFIG = {
  // 工单间延迟 (固定范围)
  ITEM_DELAY: {
    min: 10,    // 最小10秒
    max: 30     // 最大30秒
  },

  // 批次间延迟 (固定范围)
  BATCH_DELAY: {
    min: 30,    // 最小30秒
    max: 70     // 最大70秒
  }
}

// 使用场景
await randomDelay(DELAY_CONFIG.ITEM_DELAY.min, DELAY_CONFIG.ITEM_DELAY.max);   // 工单间延迟
await randomDelay(DELAY_CONFIG.BATCH_DELAY.min, DELAY_CONFIG.BATCH_DELAY.max); // 批次间延迟
```

### 3. 批量处理配置参数
```javascript
// 批量处理配置 (可配置变量)
const BATCH_CONFIG = {
  // 默认批次大小 (变量: 用户可配置)
  DEFAULT_BATCH_SIZE: 10,

  // 最大批次大小 (固定值: 系统限制)
  MAX_BATCH_SIZE: 50,

  // 最小批次大小 (固定值: 系统限制)
  MIN_BATCH_SIZE: 1,

  // 任务超时时间 (固定值: 毫秒)
  TASK_TIMEOUT: 300000,  // 5分钟

  // 重试次数 (固定值)
  MAX_RETRY_COUNT: 3
}

// 任务状态枚举 (固定值)
const TASK_STATUS = {
  PENDING: 'pending',       // 等待处理
  PROCESSING: 'processing', // 处理中
  COMPLETED: 'completed',   // 已完成
  FAILED: 'failed'         // 失败
}
```

### 4. 文件存储配置
```javascript
// 任务数据存储配置 (固定值)
const STORAGE_CONFIG = {
  // 任务存储目录 (固定路径)
  TASKS_DIR: 'server/data/tasks',

  // 文件扩展名 (固定值)
  FILE_EXTENSION: '.json',

  // 文件编码 (固定值)
  FILE_ENCODING: 'utf8',

  // JSON格式化缩进 (固定值)
  JSON_INDENT: 2
}

// 环境变量配置 (运行时变量)
const ENV_CONFIG = {
  // 用户令牌 (变量: 运行时设置)
  USER_TOKEN: process.env.USER_TOKEN || '',

  // 公司ID (变量: 运行时设置)
  COMPANY_ID: process.env.COMPANY_ID || '',

  // 调试模式 (固定值)
  DEBUG: false
}
```

### 3. 内存管理
- **任务持久化**: 任务数据保存到文件系统
- **内存清理**: 完成的任务从内存中移除
- **状态恢复**: 服务重启后恢复未完成任务

## 错误处理机制

### 1. API错误分类
```javascript
// 网络错误
if (error.request) {
  throw new Error('网络错误：服务器未响应，请检查网络连接')
}

// 服务器错误
if (error.response) {
  const status = error.response.status
  const errorMsg = error.response.data?.header?.errorMessage || '未知错误'
  throw new Error(`服务器错误(${status}): ${errorMsg}`)
}

// 业务逻辑错误
if (response.data.body.isSuccessful === false) {
  throw new Error(`请求失败：${response.data.header?.errorMessage || '未知错误'}`)
}
```

### 2. Excel数据验证
```javascript
// 必填字段验证
const requiredFields = ['报修用户', '故障描述', '处理结果', '完成时间（注意格式）', '报修时间（注意格式）']

// 日期格式验证
const datePattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/

// 数值范围验证
const isValidRemoteFlag = (value) => value === 0 || value === 1
const isValidSopFlag = (value) => value === 0 || value === 1
```

## 安全机制

### 1. 身份验证
- **Token机制**: 登录后获取访问令牌
- **Token存储**: localStorage存储用户凭证
- **Token验证**: 每次API请求携带token

### 2. 请求头设置
```javascript
const headers = {
  'Content-Type': 'application/json',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}
```
```

### 环境变量
```bash
# 用户凭证（运行时设置）
USER_TOKEN=用户访问令牌
COMPANY_ID=公司ID

# API配置
API_BASE_URL=https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api
```

## 监控和日志

### 1. 任务状态监控
- **实时进度**: 显示当前处理进度
- **状态统计**: 成功/失败/总数统计
- **错误详情**: 记录失败原因

### 2. 日志记录
```javascript
// 调试日志开关
const DEBUG = false;

// 条件日志输出
if (DEBUG) console.log('调试信息:', data);

// 错误日志
console.error('处理失败:', error);
```

## 常见问题和解决方案

### 1. API调用失败
**问题**: 网络超时或服务器错误
**解决**:
- 检查网络连接
- 验证API地址和参数
- 查看服务器响应状态

### 2. Excel格式错误
**问题**: 数据格式不符合要求
**解决**:
- 下载标准模板
- 检查必填字段
- 验证日期时间格式

### 3. 门店信息查询失败
**问题**: 门店名称不存在或不匹配
**解决**:
- 确认门店名称准确性
- 检查门店是否在系统中存在
- 使用模糊匹配机制

### 4. 批量处理中断
**问题**: 任务处理过程中服务重启
**解决**:
- 任务状态持久化到文件
- 服务重启后自动恢复未完成任务
- 支持手动重新处理失败项

## API测试示例

### 使用curl测试登录接口
```bash
curl -X POST https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "code": "HXCS_APP_SJDL"
    },
    "body": {
      "companyId": null,
      "token": "",
      "userName": "your_username",
      "passWord": "your_password",
      "deviceId": "your_username",
      "language": "zh",
      "osType": "2"
    }
  }'
```

### 使用curl测试门店查询
```bash
curl -X POST https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "code": "HXCS_ZAPP_MDLB"
    },
    "body": {
      "companyId": "your_company_id",
      "token": "your_token",
      "customerName": "门店名称"
    }
  }'
```

## 版本更新记录

### v2.0 当前版本
- 支持批量工单上传
- 实现任务队列管理
- 优化错误处理机制
- 支持Docker部署

### 主要文件清单
```
前端核心文件:
- src/utils/apiService.js          # API服务封装
- src/utils/excelParser.js         # Excel解析工具
- src/views/LoginView.vue          # 登录页面
- src/views/UploadView.vue         # 上传页面
- src/views/TaskView.vue           # 任务管理页面
- src/services/taskService.js      # 任务服务


后端核心文件:
- server/src/services/taskService.ts # 任务处理服务
- server/src/routes/task.ts          # 任务路由
- server/src/types/task.ts           # 类型定义

配置文件:
- docker-compose.yml               # Docker配置
- package.json                     # 项目依赖
- vite.config.js                   # 构建配置
```

---

**文档最后更新**: 2024年当前日期
**维护人员**: 开发团队
**联系方式**: 项目仓库Issues
