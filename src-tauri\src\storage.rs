use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use chrono::{DateTime, Utc};

// 用户凭证结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserCredentials {
    pub token: String,
    pub company_id: String,
    pub user_id: String,
    pub username: String,
    pub expires_at: DateTime<Utc>,
}

// 任务状态枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum TaskStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
}

// 任务数据结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Task {
    pub id: String,
    pub user_id: String,
    pub status: TaskStatus,
    pub total_count: usize,
    pub completed_count: usize,
    pub failed_count: usize,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub work_orders: Vec<WorkOrder>,
}

// 工单数据结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WorkOrder {
    pub store_name: String,
    pub description: String,
    pub result: String,
    pub completion_time: String,
    pub report_time: String,
    pub is_remote: i32,
    pub has_sop: i32,
    pub sop_description: String,
    pub status: String, // "pending", "success", "failed"
    pub error_message: Option<String>,
}

// 存储管理器
pub struct StorageManager {
    app_data_dir: PathBuf,
}

impl StorageManager {
    pub fn new() -> Result<Self, String> {
        let app_data_dir = dirs::data_dir()
            .ok_or("无法获取应用数据目录")?
            .join("haidilao-workorder");

        // 确保目录存在
        fs::create_dir_all(&app_data_dir)
            .map_err(|e| format!("创建应用数据目录失败: {}", e))?;

        Ok(Self { app_data_dir })
    }

    // 保存用户凭证
    pub fn save_credentials(&self, credentials: &UserCredentials) -> Result<(), String> {
        let credentials_path = self.app_data_dir.join("credentials.json");
        let json = serde_json::to_string_pretty(credentials)
            .map_err(|e| format!("序列化凭证失败: {}", e))?;
        
        fs::write(credentials_path, json)
            .map_err(|e| format!("保存凭证失败: {}", e))
    }

    // 加载用户凭证
    pub fn load_credentials(&self) -> Result<Option<UserCredentials>, String> {
        let credentials_path = self.app_data_dir.join("credentials.json");
        
        if !credentials_path.exists() {
            return Ok(None);
        }

        let json = fs::read_to_string(credentials_path)
            .map_err(|e| format!("读取凭证文件失败: {}", e))?;
        
        let credentials: UserCredentials = serde_json::from_str(&json)
            .map_err(|e| format!("解析凭证失败: {}", e))?;

        // 检查是否过期
        if credentials.expires_at < Utc::now() {
            return Ok(None);
        }

        Ok(Some(credentials))
    }

    // 清除用户凭证
    pub fn clear_credentials(&self) -> Result<(), String> {
        let credentials_path = self.app_data_dir.join("credentials.json");
        
        if credentials_path.exists() {
            fs::remove_file(credentials_path)
                .map_err(|e| format!("删除凭证文件失败: {}", e))?;
        }

        Ok(())
    }

    // 保存任务
    pub fn save_task(&self, task: &Task) -> Result<(), String> {
        let tasks_dir = self.app_data_dir.join("tasks");
        fs::create_dir_all(&tasks_dir)
            .map_err(|e| format!("创建任务目录失败: {}", e))?;

        let task_path = tasks_dir.join(format!("{}.json", task.id));
        let json = serde_json::to_string_pretty(task)
            .map_err(|e| format!("序列化任务失败: {}", e))?;
        
        fs::write(task_path, json)
            .map_err(|e| format!("保存任务失败: {}", e))
    }

    // 加载任务
    pub fn load_task(&self, task_id: &str) -> Result<Option<Task>, String> {
        let task_path = self.app_data_dir.join("tasks").join(format!("{}.json", task_id));
        
        if !task_path.exists() {
            return Ok(None);
        }

        let json = fs::read_to_string(task_path)
            .map_err(|e| format!("读取任务文件失败: {}", e))?;
        
        let task: Task = serde_json::from_str(&json)
            .map_err(|e| format!("解析任务失败: {}", e))?;

        Ok(Some(task))
    }

    // 获取用户的所有任务
    pub fn get_user_tasks(&self, user_id: &str) -> Result<Vec<Task>, String> {
        let tasks_dir = self.app_data_dir.join("tasks");
        
        if !tasks_dir.exists() {
            return Ok(Vec::new());
        }

        let mut tasks = Vec::new();
        let entries = fs::read_dir(tasks_dir)
            .map_err(|e| format!("读取任务目录失败: {}", e))?;

        for entry in entries {
            let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                let json = fs::read_to_string(&path)
                    .map_err(|e| format!("读取任务文件失败: {}", e))?;
                
                if let Ok(task) = serde_json::from_str::<Task>(&json) {
                    if task.user_id == user_id {
                        tasks.push(task);
                    }
                }
            }
        }

        // 按创建时间排序
        tasks.sort_by(|a, b| b.created_at.cmp(&a.created_at));
        Ok(tasks)
    }
}
