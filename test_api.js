// 测试API功能的脚本
const { invoke } = window.__TAURI__.tauri;

// 测试数据
const testCredentials = {
  username: "19583751050",
  password: "123456",
  company_id: "test_company"
};

// 测试登录
async function testLogin() {
  console.log("🔐 测试登录...");
  try {
    const result = await invoke('login', {
      username: testCredentials.username,
      password: testCredentials.password
    });
    console.log("✅ 登录成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 登录失败:", error);
    throw error;
  }
}

// 测试门店查询
async function testQueryStore() {
  console.log("🏪 测试门店查询...");
  try {
    const result = await invoke('query_store', {
      request: {
        customer_name: "测试门店",
        username: testCredentials.username,
        password: testCredentials.password,
        company_id: testCredentials.company_id
      }
    });
    console.log("✅ 门店查询成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 门店查询失败:", error);
    throw error;
  }
}

// 测试工单提交
async function testSubmitWorkOrder() {
  console.log("📝 测试工单提交...");
  try {
    const result = await invoke('submit_work_order', {
      request: {
        username: testCredentials.username,
        password: testCredentials.password,
        company_id: testCredentials.company_id,
        customer_id: "test_customer_id",
        customer_name: "测试客户",
        description: "测试问题描述",
        result: "测试处理结果",
        completion_time: "2024-01-15 14:30",
        report_time: "2024-01-15 15:00",
        is_remote: 0,
        has_sop: 1,
        sop_description: "测试SOP描述"
      }
    });
    console.log("✅ 工单提交成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 工单提交失败:", error);
    throw error;
  }
}

// 测试Excel解析
async function testParseExcel() {
  console.log("📊 测试Excel解析...");
  try {
    const result = await invoke('parse_excel_file', {
      filePath: "D:\\个人开发\\apple-login-upload (1)\\录单最新脚本.xlsx"
    });
    console.log("✅ Excel解析成功:", result);
    return result;
  } catch (error) {
    console.error("❌ Excel解析失败:", error);
    throw error;
  }
}

// 运行所有测试
async function runAllTests() {
  console.log("🚀 开始API测试...");
  
  try {
    // 测试登录
    await testLogin();
    
    // 测试门店查询
    await testQueryStore();
    
    // 测试工单提交
    await testSubmitWorkOrder();
    
    // 测试Excel解析
    await testParseExcel();
    
    console.log("🎉 所有测试完成!");
  } catch (error) {
    console.error("💥 测试过程中出现错误:", error);
  }
}

// 导出测试函数
window.testAPI = {
  testLogin,
  testQueryStore,
  testSubmitWorkOrder,
  testParseExcel,
  runAllTests
};

console.log("📋 测试脚本已加载，可以在控制台中调用:");
console.log("- testAPI.testLogin() - 测试登录");
console.log("- testAPI.testQueryStore() - 测试门店查询");
console.log("- testAPI.testSubmitWorkOrder() - 测试工单提交");
console.log("- testAPI.testParseExcel() - 测试Excel解析");
console.log("- testAPI.runAllTests() - 运行所有测试");
