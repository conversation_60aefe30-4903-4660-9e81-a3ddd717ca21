@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* 更新为苹果风格的设计令牌 */
  --background: oklch(1 0 0); /* #ffffff */
  --foreground: oklch(0.2 0 0); /* #1f2937 */
  --card: oklch(0.97 0 0); /* #f1f5f9 */
  --card-foreground: oklch(0.2 0 0); /* #1f2937 */
  --popover: oklch(1 0 0); /* #ffffff */
  --popover-foreground: oklch(0.2 0 0); /* #1f2937 */
  --primary: oklch(0.55 0.2 250); /* #007aff Apple Blue */
  --primary-foreground: oklch(1 0 0); /* #ffffff */
  --secondary: oklch(0.97 0 0); /* #f1f5f9 */
  --secondary-foreground: oklch(0.2 0 0); /* #1f2937 */
  --muted: oklch(0.9 0 0); /* #e5e5e5 */
  --muted-foreground: oklch(0.2 0 0); /* #1f2937 */
  --accent: oklch(0.55 0.2 250); /* #007aff */
  --accent-foreground: oklch(1 0 0); /* #ffffff */
  --destructive: oklch(0.6 0.2 30); /* #ea580c */
  --destructive-foreground: oklch(1 0 0); /* #ffffff */
  --border: oklch(0.9 0 0); /* #e5e5e5 */
  --input: oklch(1 0 0); /* #ffffff */
  --ring: oklch(0.55 0.2 250 / 0.5); /* Apple Blue with opacity */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.5rem; /* 风格的圆角 */
  --sidebar: oklch(0.97 0 0); /* #f1f5f9 */
  --sidebar-foreground: oklch(0.2 0 0); /* #1f2937 */
  --sidebar-primary: oklch(1 0 0); /* #ffffff */
  --sidebar-primary-foreground: oklch(0.2 0 0); /* #1f2937 */
  --sidebar-accent: oklch(0.55 0.2 250); /* #007aff */
  --sidebar-accent-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-border: oklch(0.9 0 0); /* #e5e5e5 */
  --sidebar-ring: oklch(0.55 0.2 250 / 0.5); /* Apple Blue with opacity */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.55 0.2 250); /* 保持苹果蓝色 */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.55 0.2 250);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.55 0.2 250 / 0.5);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  /* 添加苹果风格字体变量 */
  --font-sans: "Space Grotesk", ui-sans-serif, system-ui, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}
