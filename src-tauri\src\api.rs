use serde::{Deserialize, Serialize};

// API基础URL
const API_BASE_URL: &str = "https://kf.uhi-networks.com/asset-api-gateway/compAppGateWay/api";

// 通用API请求结构
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiRequest<T> {
    pub header: ApiHeader,
    pub body: T,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiHeader {
    pub code: String,
}

// 通用API响应结构
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub header: ResponseHeader,
    pub body: ResponseBody<T>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseHeader {
    #[serde(rename = "errorMessage")]
    pub error_message: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseBody<T> {
    #[serde(rename = "isSuccessful")]
    pub is_successful: bool,
    #[serde(rename = "resultData")]
    pub result_data: Option<T>,
}

// 登录请求体
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    #[serde(rename = "companyId")]
    pub company_id: Option<String>,
    pub token: String,
    #[serde(rename = "userName")]
    pub user_name: String,
    #[serde(rename = "passWord")]
    pub pass_word: String,
    #[serde(rename = "deviceId")]
    pub device_id: String,
    pub language: String,
    #[serde(rename = "osType")]
    pub os_type: String,
}

// 登录响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    #[serde(rename = "companyId")]
    pub company_id: String,
    #[serde(rename = "userId")]
    pub user_id: String,
}

// 门店查询请求体
#[derive(Debug, Serialize, Deserialize)]
pub struct StoreQueryRequest {
    #[serde(rename = "companyId")]
    pub company_id: String,
    pub token: String,
    #[serde(rename = "customerName")]
    pub customer_name: String,
}

// 门店查询响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct StoreQueryResponse {
    #[serde(rename = "customerId")]
    pub customer_id: String,
    #[serde(rename = "customerName")]
    pub customer_name: String,
}

// 工单提交请求体
#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderSubmitRequest {
    #[serde(rename = "companyId")]
    pub company_id: String,
    pub token: String,
    #[serde(rename = "customerId")]
    pub customer_id: String,
    #[serde(rename = "customerName")]
    pub customer_name: String,
    pub description: String,
    pub result: String,
    #[serde(rename = "completionTime")]
    pub completion_time: String,
    #[serde(rename = "reportTime")]
    pub report_time: String,
    #[serde(rename = "isRemote")]
    pub is_remote: i32,
    #[serde(rename = "hasSop")]
    pub has_sop: i32,
    #[serde(rename = "sopDescription")]
    pub sop_description: String,
}

// 工单提交响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderSubmitResponse {
    #[serde(rename = "workOrderId")]
    pub work_order_id: String,
    pub status: String,
}

// API客户端
pub struct ApiClient {
    client: reqwest::Client,
}

impl ApiClient {
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::new(),
        }
    }

    // 用户登录
    pub async fn login(&self, username: &str, password: &str) -> Result<LoginResponse, String> {
        let request = ApiRequest {
            header: ApiHeader {
                code: "HXCS_APP_SJDL".to_string(),
            },
            body: LoginRequest {
                company_id: None,
                token: "".to_string(),
                user_name: username.to_string(),
                pass_word: password.to_string(),
                device_id: username.to_string(),
                language: "zh".to_string(),
                os_type: "2".to_string(),
            },
        };

        let response = self
            .client
            .post(API_BASE_URL)
            .json(&request)
            .send()
            .await
            .map_err(|e| format!("网络请求失败: {}", e))?;

        let api_response: ApiResponse<LoginResponse> = response
            .json()
            .await
            .map_err(|e| format!("响应解析失败: {}", e))?;

        if api_response.body.is_successful {
            api_response.body.result_data
                .ok_or_else(|| "登录成功但未返回数据".to_string())
        } else {
            Err(api_response.header.error_message
                .unwrap_or_else(|| "登录失败".to_string()))
        }
    }

    // 门店信息查询
    pub async fn query_store(&self, company_id: &str, token: &str, customer_name: &str) -> Result<StoreQueryResponse, String> {
        let request = ApiRequest {
            header: ApiHeader {
                code: "HXCS_ZAPP_MDLB".to_string(),
            },
            body: StoreQueryRequest {
                company_id: company_id.to_string(),
                token: token.to_string(),
                customer_name: customer_name.to_string(),
            },
        };

        let response = self
            .client
            .post(API_BASE_URL)
            .json(&request)
            .send()
            .await
            .map_err(|e| format!("网络请求失败: {}", e))?;

        let api_response: ApiResponse<StoreQueryResponse> = response
            .json()
            .await
            .map_err(|e| format!("响应解析失败: {}", e))?;

        if api_response.body.is_successful {
            api_response.body.result_data
                .ok_or_else(|| "查询成功但未返回数据".to_string())
        } else {
            Err(api_response.header.error_message
                .unwrap_or_else(|| "门店查询失败".to_string()))
        }
    }

    // 工单提交
    pub async fn submit_work_order(
        &self,
        company_id: &str,
        token: &str,
        customer_id: &str,
        customer_name: &str,
        description: &str,
        result: &str,
        completion_time: &str,
        report_time: &str,
        is_remote: i32,
        has_sop: i32,
        sop_description: &str,
    ) -> Result<WorkOrderSubmitResponse, String> {
        let request = ApiRequest {
            header: ApiHeader {
                code: "HXCS_ZAPP_FWGDTB".to_string(),
            },
            body: WorkOrderSubmitRequest {
                company_id: company_id.to_string(),
                token: token.to_string(),
                customer_id: customer_id.to_string(),
                customer_name: customer_name.to_string(),
                description: description.to_string(),
                result: result.to_string(),
                completion_time: completion_time.to_string(),
                report_time: report_time.to_string(),
                is_remote,
                has_sop,
                sop_description: sop_description.to_string(),
            },
        };

        let response = self
            .client
            .post(API_BASE_URL)
            .json(&request)
            .send()
            .await
            .map_err(|e| format!("网络请求失败: {}", e))?;

        let api_response: ApiResponse<WorkOrderSubmitResponse> = response
            .json()
            .await
            .map_err(|e| format!("响应解析失败: {}", e))?;

        if api_response.body.is_successful {
            api_response.body.result_data
                .ok_or_else(|| "工单提交成功但未返回数据".to_string())
        } else {
            Err(api_response.header.error_message
                .unwrap_or_else(|| "工单提交失败".to_string()))
        }
    }
}
