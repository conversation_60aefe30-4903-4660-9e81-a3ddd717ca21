{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 16243440802910076984, "profile": 2040997289075261528, "path": 10763286916239946207, "deps": [[503635761244294217, "regex", false, 16719734492345273414], [1209499463363567426, "reqwest", false, 12344681074581810549], [1322478694103194923, "build_script_build", false, 696713411568064653], [2995469292676432503, "uuid", false, 13265478025197709892], [4352886507220678900, "serde_json", false, 12607692758742004834], [5986029879202738730, "log", false, 4124725217899912772], [8256202458064874477, "dirs", false, 11276291506866168096], [8324462083842905811, "tauri_plugin_log", false, 16140452845700976369], [9689903380558560274, "serde", false, 10456939553781973694], [9897246384292347999, "chrono", false, 7821443858785870503], [13977539651438937551, "calamine", false, 3525404009341163933], [17045726903344650895, "tauri", false, 13724076585772620961], [17531218394775549125, "tokio", false, 4873622909697905687]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\app-4e7dc2d6835c5d74\\dep-lib-app_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}